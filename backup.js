
/**
 * Finds the latest version of pg_dump available in the system
 * @returns {Promise<string>} The command to use for pg_dump
 */
async function findLatestPgDump() {
    const { exec } = require('child_process');
    const util = require('util');
    const execPromise = util.promisify(exec);

    try {
        // First try to get the version of the default pg_dump
        const { stdout: versionOutput } = await execPromise('pg_dump --version');

        // Check if we can find PostgreSQL installations in standard locations
        let pgDumpPaths = [];
        try {
            // Look for PostgreSQL installations in standard locations
            const { stdout: findOutput } = await execPromise('find /usr/lib/postgresql/ -name pg_dump -type f 2>/dev/null || echo ""');
            if (findOutput.trim()) {
                pgDumpPaths = findOutput.trim().split('\n').filter(Boolean);

                if (pgDumpPaths.length > 0) {
                    // Sort paths to get the latest version (assuming version is in the path)
                    pgDumpPaths.sort().reverse();
                    return pgDumpPaths[0]; // Return the path to the latest version
                }
            }
        } catch (findError) {
            console.warn(`Error searching for pg_dump installations: ${findError.message}`);
        }

        // If we couldn't find a specific version or there was an error, use the default
        return 'pg_dump';
    } catch (error) {
        console.warn(`Error determining pg_dump version: ${error.message}`);
        // Fall back to the default pg_dump command
        return 'pg_dump';
    }
}

function generateListFromEnv() {
    // Find all environment variables that start with "BACKUP_"
    const backupEnvVars = Object.keys(process.env).filter(key => key.startsWith('BACKUP_'));

    // Group them by the next key in the name
    const groupedEnvVars = {};

    backupEnvVars.forEach(key => {
        // Extract the group identifier (e.g., "CK" from "BACKUP_CK_NAME")
        const parts = key.split('_');
        if (parts.length >= 3) {
            const groupName = parts[1];

            // Initialize the group if it doesn't exist
            if (!groupedEnvVars[groupName]) {
                groupedEnvVars[groupName] = {};
            }

            // Store the variable in its group with the remaining part as the key
            const remainingKey = parts.slice(2).join('_');
            groupedEnvVars[groupName][remainingKey] = process.env[key];
        }
    });

    // Process each group into a list of backup configurations
    const backupConfigs = [];
    for (const groupId in groupedEnvVars) {
        const group = groupedEnvVars[groupId];

        // Extract the parameters needed for the backup function
        const name = group['NAME'] || groupId;
        const connectionString = group['DB_CONNECTION_STRING'];
        const storageToken = group['STORAGE_TOKEN'];
        const folderName = `dbBackups_${groupId}`; // Always use dbBackups_<groupId> format

        // Check if we have the required parameters
        if (connectionString && storageToken) {
            backupConfigs.push({
                name,
                connectionString,
                storageToken,
                folderName,
                groupId: groupId
            });
        } else {
            console.warn(`Skipping backup for group ${groupId}: Missing required parameters`);
        }
    }

    return backupConfigs;
}

async function backup(name, folderName, connectionString, storageToken, pgDumpCommand) {

    var exec = require('child_process').exec;
    var put = require('@vercel/blob').put;

    // Build date+time string
    process.env.TZ = 'Australia/Perth';
    let theDate = new Date();
    let year = theDate.getFullYear();
    let month = ('0'+(theDate.getMonth()+1)).slice(-2);
    let day = ('0'+(theDate.getDate())).slice(-2);
    let hour = ('0'+(theDate.getHours())).slice(-2);
    let minutes = ('0'+(theDate.getMinutes())).slice(-2);
    let dateTimeString = `${year}-${month}-${day} ${hour}${minutes}`;

    // Backup
    let dbBackupString = "";
    await new Promise((resolve) => {
        exec(
            `${pgDumpCommand} --inserts --clean --no-privileges --if-exists \"${connectionString}\"`,
            (error, stdout, stderr) => {
                if (error) {
                    console.error(`pg_dump error: ${stderr}`);
                    dbBackupString = `ERROR: ${stderr}`;
                } else {
                    dbBackupString = stdout != null && stdout != "" ? stdout : stderr;
                }
                resolve("Received DB backup successfully.");
            }
        );
    });

    // Check if the backup string contains a pg_dump error
    if (dbBackupString.includes("pg_dump: error") || dbBackupString.startsWith("ERROR:")) {
        // Log the error instead of sending it to storage
        console.error(`Backup for ${name} failed with pg_dump error:`);
        console.error(dbBackupString);
        console.log(`Skipping storage upload for ${name} due to pg_dump error`);
    } else {
        // Save to blob storage using the project-specific folder
        await put(`${folderName}/${name} (${dateTimeString}).sql`, dbBackupString, {
            access: 'public',
            token: storageToken,
            addRandomSuffix: false // Prevent random suffix from being added to the filename
        });
        console.log(`Backup successful for project: ${name}`);
    }

}

// Main execution function
(async function main() {
try {
    console.log();

    // Get the list of backup configurations from environment variables
    const backupConfigs = generateListFromEnv();

    // Find the latest pg_dump version once before running any backups
    const pgDumpCommand = await findLatestPgDump();

    // Process each configuration
    const backupPromises = [];
    for (const config of backupConfigs) {
        console.log(`Starting backup for project: ${config.name}`);
        backupPromises.push(backup(
            config.name,
            config.folderName,
            config.connectionString,
            config.storageToken,
            pgDumpCommand
        ));
    }

    console.log();

    // Wait for all backups to complete
    if (backupPromises.length > 0) {
        Promise.all(backupPromises).then(() => {
            console.log('\nAll backups completed successfully');
        });
    } else {
        console.warn('\nNo valid backup configurations found in environment variables');
    }
} catch (error) {
    console.error('\nError during backup process:', error);
}
})();