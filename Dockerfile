# Use the official Node.js image
FROM node:20

# Set working directory
WORKDIR /app

# Install the latest PostgreSQL client tools
RUN apt-get update && \
    apt-get install -y curl gnupg lsb-release && \
    curl -fsSL https://www.postgresql.org/media/keys/ACCC4CF8.asc | gpg --dearmor -o /usr/share/keyrings/postgresql-keyring.gpg && \
    echo "deb [signed-by=/usr/share/keyrings/postgresql-keyring.gpg] http://apt.postgresql.org/pub/repos/apt/ $(lsb_release -cs)-pgdg main" > /etc/apt/sources.list.d/pgdg.list && \
    apt-get update && \
    # Install the latest PostgreSQL client tools by not specifying a version
    apt-get install -y postgresql-client && \
    apt-get clean

# Copy package files first (to leverage Docker cache)
COPY package*.json ./

# Install dependencies (if any)
RUN npm install

# Copy the rest of the files
COPY . .

# Run the script
CMD ["node", "backup.js"]